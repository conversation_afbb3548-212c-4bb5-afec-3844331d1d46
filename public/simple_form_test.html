<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Form Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-2xl mx-auto bg-white p-6 rounded-lg shadow">
        <h1 class="text-2xl font-bold mb-6">Simple Leave Request Form Test</h1>
        
        <form action="/nouvelle-demande" method="POST" id="simpleForm">
            <div class="mb-4">
                <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Type de demande *</label>
                <select id="type" name="type" required class="w-full border border-gray-300 rounded-md px-3 py-2">
                    <option value="">-- Sélectionnez un type --</option>
                    <option value="payé">Congé payé</option>
                    <option value="sans solde">Congé sans solde</option>
                    <option value="maladie">Congé maladie</option>
                    <option value="exceptionnel">Congé exceptionnel</option>
                </select>
            </div>
            
            <div class="mb-4">
                <label for="date_debut" class="block text-sm font-medium text-gray-700 mb-2">Date de début *</label>
                <input type="date" id="date_debut" name="date_debut" required class="w-full border border-gray-300 rounded-md px-3 py-2">
            </div>
            
            <div class="mb-4">
                <label for="date_fin" class="block text-sm font-medium text-gray-700 mb-2">Date de fin *</label>
                <input type="date" id="date_fin" name="date_fin" required class="w-full border border-gray-300 rounded-md px-3 py-2">
            </div>
            
            <div class="mb-4">
                <label for="motif" class="block text-sm font-medium text-gray-700 mb-2">Motif</label>
                <textarea id="motif" name="motif" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2"></textarea>
            </div>
            
            <div class="mb-6">
                <label for="priorite" class="block text-sm font-medium text-gray-700 mb-2">Priorité</label>
                <select id="priorite" name="priorite" class="w-full border border-gray-300 rounded-md px-3 py-2">
                    <option value="normale">Normale</option>
                    <option value="haute">Haute</option>
                    <option value="urgente">Urgente</option>
                </select>
            </div>
            
            <div class="flex gap-4">
                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Soumettre (Simple)
                </button>
                <button type="button" onclick="testWithoutJS()" class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                    Test sans JS
                </button>
                <button type="button" onclick="testValidation()" class="px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
                    Test Validation
                </button>
            </div>
        </form>
        
        <div id="results" class="mt-6 p-4 bg-gray-100 rounded-md hidden">
            <h3 class="font-bold mb-2">Résultats du test:</h3>
            <pre id="resultContent"></pre>
        </div>
    </div>

    <script>
        // Set default dates (10 days from now)
        const today = new Date();
        const startDate = new Date(today);
        startDate.setDate(today.getDate() + 10);
        const endDate = new Date(today);
        endDate.setDate(today.getDate() + 12);
        
        document.getElementById('date_debut').value = startDate.toISOString().split('T')[0];
        document.getElementById('date_fin').value = endDate.toISOString().split('T')[0];
        document.getElementById('type').value = 'payé';
        document.getElementById('motif').value = 'Test de soumission simple';

        function showResult(message) {
            document.getElementById('results').classList.remove('hidden');
            document.getElementById('resultContent').textContent = message;
        }

        function testWithoutJS() {
            // Remove any JavaScript validation and submit directly
            const form = document.getElementById('simpleForm');
            const newForm = form.cloneNode(true);
            form.parentNode.replaceChild(newForm, form);
            newForm.submit();
        }

        function testValidation() {
            const formData = {
                type: document.getElementById('type').value,
                date_debut: document.getElementById('date_debut').value,
                date_fin: document.getElementById('date_fin').value,
                demi_journee: false,
                demi_type: null
            };

            showResult('Testing validation...');

            fetch('/demandes/validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                showResult('Validation successful:\n' + JSON.stringify(data, null, 2));
            })
            .catch(error => {
                showResult('Validation error:\n' + error.message);
                console.error('Validation error:', error);
            });
        }

        // Simple form submission with basic validation
        document.getElementById('simpleForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const type = document.getElementById('type').value;
            const startDate = document.getElementById('date_debut').value;
            const endDate = document.getElementById('date_fin').value;
            
            if (!type || !startDate || !endDate) {
                alert('Veuillez remplir tous les champs obligatoires.');
                return;
            }
            
            if (new Date(endDate) < new Date(startDate)) {
                alert('La date de fin doit être postérieure ou égale à la date de début.');
                return;
            }
            
            showResult('Form validation passed. Submitting...');
            
            // Submit the form normally
            setTimeout(() => {
                this.submit();
            }, 1000);
        });
    </script>
</body>
</html>
