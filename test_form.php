<?php
// Test script to check leave request form functionality
session_start();

// Define paths
define('ROOT_PATH', dirname(__FILE__));
define('APP_PATH', ROOT_PATH . '/app');
define('CORE_PATH', ROOT_PATH . '/core');
define('CONFIG_PATH', ROOT_PATH . '/config');

// Include core files
require_once CORE_PATH . '/Autoloader.php';
require_once CORE_PATH . '/Controller.php';
require_once CORE_PATH . '/Model.php';
require_once CORE_PATH . '/Database.php';
require_once CORE_PATH . '/Auth.php';
require_once CONFIG_PATH . '/config.php';

// Register autoloader
Autoloader::register();

// Test 1: Check if we can create a test user and authenticate
echo "=== Testing Leave Request Form Functionality ===\n\n";

try {
    // Create database connection
    $db = Database::getInstance();
    $pdo = $db->getConnection();

    echo "✓ Database connection successful\n";

    // Use existing test user (employe1)
    $stmt = $pdo->prepare("SELECT * FROM users WHERE login = ?");
    $stmt->execute(['employe1']);
    $testUser = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$testUser) {
        echo "❌ Test user 'employe1' not found\n";
        exit(1);
    } else {
        echo "✓ Using existing test user: " . $testUser['login'] . " (" . $testUser['role'] . ")\n";
    }

    // Simulate user session
    $_SESSION['user_id'] = $testUser['id'];
    $_SESSION['role'] = $testUser['role'];
    $_SESSION['nom'] = $testUser['nom'];
    $_SESSION['prenom'] = $testUser['prenom'];

    echo "✓ User session simulated\n";

    // Test 2: Check if DemandeController can be instantiated
    echo "\nTesting DemandeController instantiation...\n";
    $demandeController = new DemandeController();
    echo "✓ DemandeController instantiated successfully\n";

    // Test 3: Check if models are working
    echo "\nTesting models...\n";
    $demandeModel = new DemandeModel();
    $userModel = new UserModel();
    $leaveBalanceModel = new LeaveBalanceModel();
    echo "✓ All models instantiated successfully\n";

    // Test 4: Check if we can get user leave balances
    echo "\nTesting leave balance retrieval...\n";
    $leaveBalances = $leaveBalanceModel->getUserLeaveBalances($testUser['id']);
    echo "✓ Leave balances retrieved: " . json_encode($leaveBalances) . "\n";

    // Test 5: Test form validation
    echo "\nTesting form validation...\n";

    // Simulate POST data for a valid leave request
    $_POST = [
        'type' => 'payé',
        'date_debut' => date('Y-m-d', strtotime('+10 days')), // 10 days from now
        'date_fin' => date('Y-m-d', strtotime('+12 days')), // 12 days from now
        'motif' => 'Test leave request',
        'demi_jour_debut' => '',
        'demi_jour_fin' => '',
        'periode_debut' => 'matin',
        'periode_fin' => 'apres-midi'
    ];
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['REQUEST_URI'] = '/nouvelle-demande';

    echo "✓ POST data simulated\n";

    // Test 6: Test the validation endpoint
    echo "\nTesting validation endpoint...\n";

    // Simulate JSON request for validation
    $validationData = [
        'type' => 'payé',
        'date_debut' => date('Y-m-d', strtotime('+10 days')),
        'date_fin' => date('Y-m-d', strtotime('+12 days')),
        'demi_journee' => false,
        'demi_type' => null
    ];

    // Test validation method directly
    $validation = $demandeModel->validateLeaveRequest(
        $testUser['id'],
        $validationData['date_debut'],
        $validationData['date_fin'],
        $validationData['type'],
        $validationData['demi_journee'],
        $validationData['demi_type']
    );

    echo "✓ Validation result: " . json_encode($validation) . "\n";

    if ($validation['valid']) {
        echo "✓ Form validation passed\n";
    } else {
        echo "✗ Form validation failed: " . implode(', ', $validation['errors']) . "\n";
    }

    echo "\n=== Test Summary ===\n";
    echo "✓ Database connection: OK\n";
    echo "✓ User authentication: OK\n";
    echo "✓ Controller instantiation: OK\n";
    echo "✓ Model instantiation: OK\n";
    echo "✓ Leave balance retrieval: OK\n";
    echo "✓ Form validation: " . ($validation['valid'] ? 'OK' : 'FAILED') . "\n";

    if ($validation['valid']) {
        echo "\n🎉 All tests passed! The leave request form should be working correctly.\n";
        echo "\nPossible issues to check:\n";
        echo "1. User authentication - make sure users can log in\n";
        echo "2. JavaScript errors - check browser console\n";
        echo "3. Form submission - check network tab for failed requests\n";
        echo "4. Session management - ensure sessions are maintained\n";
    } else {
        echo "\n❌ Form validation failed. This indicates an issue with the validation logic.\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
