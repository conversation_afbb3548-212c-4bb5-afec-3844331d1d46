<?php
// This is a partial for the responsable (manager) sidebar
?>

<!-- Mobile Header - Only visible on small screens -->
<header class="mobile-header">
    <div class="flex items-center">
        <button id="mobileMenuButton" class="mr-2 text-white focus:outline-none">
            <i class="fas fa-bars text-xl"></i>
        </button>
        <span class="text-lg font-bold">GestionConge</span>
    </div>
    <div class="flex items-center">
        <a href="/notifications" class="relative mr-4">
            <i class="fas fa-bell text-white text-lg"></i>
            <?php if (isset($notificationCount) && $notificationCount > 0): ?>
            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full"><?= $notificationCount ?></span>
            <?php endif; ?>
        </a>
        <a href="/responsable/demandes_approbation" class="relative mr-4">
            <i class="fas fa-check-circle text-white text-lg"></i>
            <?php if (isset($demandesCount) && $demandesCount > 0): ?>
            <span class="absolute -top-1 -right-1 bg-yellow-500 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full"><?= $demandesCount ?></span>
            <?php endif; ?>
        </a>
        <a href="/profil" class="text-white">
            <i class="fas fa-user text-lg"></i>
        </a>
    </div>
</header>

<aside id="sidebar" class="sidebar">
    <div class="sidebar-header">
        <a href="/responsable/team_availability" class="sidebar-logo">
            <i class="fas fa-calendar-check text-white text-xl mr-2"></i>
            <span class="logo-text">GestionConge</span>
        </a>
        <button id="toggleSidebar" class="hidden md:block">
            <i class="fas fa-bars"></i>
        </button>
        <button id="closeSidebar" class="md:hidden">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <div class="sidebar-nav">
        <nav>
            <a href="/responsable/team_availability" class="menu-item">
                <i class="fas fa-home"></i>
                <span class="menu-text">Tableau de bord</span>
            </a>

            <div class="sidebar-category">Équipe</div>
            <a href="/responsable/team_members" class="menu-item">
                <i class="fas fa-users"></i>
                <span class="menu-text">Membres d'équipe</span>
            </a>
            <div class="sidebar-category">Mes congés</div>
            <a href="/responsable/mes-demandes" class="menu-item">
                <i class="fas fa-list-alt"></i>
                <span class="menu-text">Mes demandes</span>
            </a>
            <div class="sidebar-category">Demandes</div>
            <a href="/responsable/demandes_approbation" class="menu-item">
                <i class="fas fa-check-circle"></i>
                <span class="menu-text">À approuver</span>
                <?php if (isset($demandesCount) && $demandesCount > 0): ?>
                <span class="notification-badge"><?= $demandesCount ?></span>
                <?php endif; ?>
            </a>
            <a href="/responsable/historique_demandes" class="menu-item">
                <i class="fas fa-history"></i>
                <span class="menu-text">Historique</span>
            </a>
            <a href="/notifications" class="menu-item">
                <i class="fas fa-bell"></i>
                <span class="menu-text">Notifications</span>
                <?php if (isset($notificationCount) && $notificationCount > 0): ?>
                <span class="notification-badge"><?= $notificationCount ?></span>
                <?php endif; ?>
            </a>
            <a href="/profil" class="menu-item">
                <i class="fas fa-user"></i>
                <span class="menu-text">Mon profil</span>
            </a>
            <a href="/logout" class="menu-item text-red-300">
                <i class="fas fa-sign-out-alt"></i>
                <span class="menu-text">Déconnexion</span>
            </a>
        </nav>
    </div>
</aside>

<script>
// For desktop - toggle sidebar collapse
document.getElementById('toggleSidebar').addEventListener('click', function() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('sidebar-collapsed');

    // Adjust main content margin
    const mainContent = document.getElementById('main-content');
    if (mainContent) {
        if (sidebar.classList.contains('sidebar-collapsed')) {
            mainContent.style.marginLeft = '60px';
        } else {
            mainContent.style.marginLeft = '240px';
        }
    }
});

// For mobile - open sidebar
document.getElementById('mobileMenuButton').addEventListener('click', function() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.add('sidebar-open');

    // Add overlay
    const overlay = document.createElement('div');
    overlay.id = 'sidebar-overlay';
    overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-40';
    document.body.appendChild(overlay);

    // Close sidebar when clicking overlay
    overlay.addEventListener('click', closeMobileSidebar);
});

// For mobile - close sidebar
document.getElementById('closeSidebar').addEventListener('click', closeMobileSidebar);

function closeMobileSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.remove('sidebar-open');

    // Remove overlay
    const overlay = document.getElementById('sidebar-overlay');
    if (overlay) {
        overlay.remove();
    }
}

// Handle resize events to ensure proper layout
window.addEventListener('resize', function() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');

    if (window.innerWidth >= 768) {
        // Remove mobile-specific classes
        sidebar.classList.remove('sidebar-open');

        // Remove overlay
        const overlay = document.getElementById('sidebar-overlay');
        if (overlay) {
            overlay.remove();
        }

        // Reset desktop layout
        if (sidebar.classList.contains('sidebar-collapsed')) {
            if (mainContent) mainContent.style.marginLeft = '60px';
        } else {
            if (mainContent) mainContent.style.marginLeft = '240px';
        }
    } else {
        // Set mobile layout
        if (mainContent) mainContent.style.marginLeft = '0';
    }
});

// Initialize on page load
window.addEventListener('DOMContentLoaded', function() {
    if (window.innerWidth < 768) {
        const mainContent = document.getElementById('main-content');
        if (mainContent) mainContent.style.marginLeft = '0';
    }
});
</script>
