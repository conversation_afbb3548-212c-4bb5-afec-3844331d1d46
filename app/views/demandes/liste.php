<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mes demandes - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <style>
        /* Pagination styles for better accessibility and mobile responsiveness */
        .pagination-btn {
            min-width: 40px;
            min-height: 40px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        .pagination-btn:focus {
            outline: 2px solid #8B5CF6;
            outline-offset: 2px;
        }

        .pagination-btn:disabled {
            pointer-events: none;
        }

        /* Mobile responsive pagination */
        @media (max-width: 640px) {
            .pagination-btn {
                min-width: 36px;
                min-height: 36px;
                font-size: 12px;
                padding: 0.5rem;
            }

            nav[aria-label="Pagination"] {
                flex-wrap: wrap;
                gap: 0.25rem;
            }
        }

        /* Loading state for pagination */
        .pagination-loading .pagination-btn {
            opacity: 0.5;
            pointer-events: none;
        }
    </style>
</head>

<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_employe.php'; ?>

    <div class="md:ml-[240px] p-4 md:p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 class="text-xl sm:text-2xl font-bold text-gray-800">Mes demandes</h1>
                    <p class="text-sm sm:text-base text-gray-600">Historique de vos demandes de congé et d'absence</p>
                </div>

                <a href="/nouvelle-demande"
                    class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition flex items-center gap-2 text-sm sm:text-base whitespace-nowrap">
                    <i class="fas fa-plus"></i> Nouvelle demande
                </a>
            </div>
        </header>

        <div class="bg-white rounded-lg shadow-sm mb-6 p-4">
            <div class="flex flex-wrap items-center justify-between mb-4">
                <div class="w-full md:w-auto mb-4 md:mb-0">
                    <div class="relative">
                        <input type="text" id="search" placeholder="Rechercher..."
                            value="<?= htmlspecialchars($filters['search'] ?? '') ?>"
                            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-auto flex flex-wrap items-center">
                    <div class="mr-2 mb-2">
                        <select id="per_page"
                            class="pl-3 pr-10 py-2 text-sm border-gray-300 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                            <option value="5"
                                <?= (isset($pagination['perPage']) && $pagination['perPage'] == 5) ? 'selected' : '' ?>>
                                5 par page</option>
                            <option value="10"
                                <?= (isset($pagination['perPage']) && $pagination['perPage'] == 10) ? 'selected' : '' ?>>
                                10 par page</option>
                            <option value="25"
                                <?= (isset($pagination['perPage']) && $pagination['perPage'] == 25) ? 'selected' : '' ?>>
                                25 par page</option>
                            <option value="50"
                                <?= (isset($pagination['perPage']) && $pagination['perPage'] == 50) ? 'selected' : '' ?>>
                                50 par page</option>
                        </select>
                    </div>
                    <button id="toggleFilters"
                        class="bg-gray-100 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-200 transition flex items-center gap-1 text-sm mb-2">
                        <i class="fas fa-sliders-h"></i> Filtres
                    </button>
                </div>
            </div>

            <div id="filterPanel"
                class="border-t pt-4 mt-2 <?= (!empty($filters['period']) && $filters['period'] !== 'all') ||
                                                              (!empty($filters['status']) && $filters['status'] !== 'all') ||
                                                              (!empty($filters['type']) && $filters['type'] !== 'all') ? '' : 'hidden' ?>">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="period" class="block text-sm font-medium text-gray-700 mb-1">Période</label>
                        <select id="period"
                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            <option value="all"
                                <?= (isset($filters['period']) && $filters['period'] === 'all') ? 'selected' : '' ?>>
                                Toutes les périodes</option>
                            <option value="30"
                                <?= (isset($filters['period']) && $filters['period'] === '30') ? 'selected' : '' ?>>30
                                derniers jours</option>
                            <option value="90"
                                <?= (isset($filters['period']) && $filters['period'] === '90') ? 'selected' : '' ?>>90
                                derniers jours</option>
                            <option value="180"
                                <?= (isset($filters['period']) && $filters['period'] === '180') ? 'selected' : '' ?>>6
                                derniers mois</option>
                            <option value="365"
                                <?= (isset($filters['period']) && $filters['period'] === '365') ? 'selected' : '' ?>>12
                                derniers mois</option>
                        </select>
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                        <select id="status"
                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            <option value="all"
                                <?= (isset($filters['status']) && $filters['status'] === 'all') ? 'selected' : '' ?>>
                                Tous les statuts</option>
                            <option value="pending"
                                <?= (isset($filters['status']) && $filters['status'] === 'pending') ? 'selected' : '' ?>>
                                En attente</option>
                            <option value="approved"
                                <?= (isset($filters['status']) && $filters['status'] === 'approved') ? 'selected' : '' ?>>
                                Approuvée</option>
                            <option value="rejected"
                                <?= (isset($filters['status']) && $filters['status'] === 'rejected') ? 'selected' : '' ?>>
                                Rejetée</option>
                            <option value="cancelled"
                                <?= (isset($filters['status']) && $filters['status'] === 'cancelled') ? 'selected' : '' ?>>
                                Annulée</option>
                        </select>
                    </div>
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Type de congé</label>
                        <select id="type"
                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            <option value="all"
                                <?= (isset($filters['type']) && $filters['type'] === 'all') ? 'selected' : '' ?>>Tous
                                les types</option>
                            <option value="cp"
                                <?= (isset($filters['type']) && $filters['type'] === 'cp') ? 'selected' : '' ?>>Congé
                                payé</option>
                            <option value="css"
                                <?= (isset($filters['type']) && $filters['type'] === 'css') ? 'selected' : '' ?>>Congé
                                sans solde</option>
                            <option value="cm"
                                <?= (isset($filters['type']) && $filters['type'] === 'cm') ? 'selected' : '' ?>>Congé
                                maladie</option>
                            <option value="cf"
                                <?= (isset($filters['type']) && $filters['type'] === 'cf') ? 'selected' : '' ?>>Congé
                                familial</option>
                        </select>
                    </div>
                </div>
                <div class="mt-4 flex justify-end">
                    <button id="filterButton"
                        class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition flex items-center gap-2">
                        <i class="fas fa-filter"></i> Appliquer les filtres
                    </button>
                    <a href="/mes-demandes"
                        class="ml-2 text-gray-600 hover:text-gray-800 underline text-sm flex items-center">
                        <i class="fas fa-times mr-1"></i> Réinitialiser
                    </a>
                </div>
            </div>
        </div>

        <?php if (empty($demandes)): ?>
        <div class="table-card">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Référence</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Début</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Fin</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Validé par</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center">
                                    <i class="fas fa-calendar-times text-4xl text-gray-300 mb-4"></i>
                                    <p class="text-lg mb-4">Vous n'avez pas encore fait de demandes de congé ou d'absence.</p>
                                    <a href="/nouvelle-demande"
                                        class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition inline-block">
                                        Créer votre première demande
                                    </a>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <?php else: ?>
        <div class="table-card">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Référence
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="#" class="sort-header flex items-center" data-sort="type">
                                    Type
                                    <?php if (isset($sorting['sortBy']) && $sorting['sortBy'] === 'type'): ?>
                                    <i
                                        class="fas fa-sort-<?= $sorting['sortOrder'] === 'ASC' ? 'up' : 'down' ?> ml-1"></i>
                                    <?php else: ?>
                                    <i class="fas fa-sort ml-1 text-gray-400"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="#" class="sort-header flex items-center" data-sort="date_debut">
                                    Date Début
                                    <?php if (isset($sorting['sortBy']) && $sorting['sortBy'] === 'date_debut'): ?>
                                    <i
                                        class="fas fa-sort-<?= $sorting['sortOrder'] === 'ASC' ? 'up' : 'down' ?> ml-1"></i>
                                    <?php else: ?>
                                    <i class="fas fa-sort ml-1 text-gray-400"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="#" class="sort-header flex items-center" data-sort="date_fin">
                                    Date Fin
                                    <?php if (isset($sorting['sortBy']) && $sorting['sortBy'] === 'date_fin'): ?>
                                    <i
                                        class="fas fa-sort-<?= $sorting['sortOrder'] === 'ASC' ? 'up' : 'down' ?> ml-1"></i>
                                    <?php else: ?>
                                    <i class="fas fa-sort ml-1 text-gray-400"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Durée</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <a href="#" class="sort-header flex items-center" data-sort="statut">
                                    Statut
                                    <?php if (isset($sorting['sortBy']) && $sorting['sortBy'] === 'statut'): ?>
                                    <i
                                        class="fas fa-sort-<?= $sorting['sortOrder'] === 'ASC' ? 'up' : 'down' ?> ml-1"></i>
                                    <?php else: ?>
                                    <i class="fas fa-sort ml-1 text-gray-400"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Validé par</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($demandes as $demande): ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?= !empty($demande['reference_demande']) ? htmlspecialchars($demande['reference_demande']) : '<span class="text-gray-400">-</span>' ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    <?php
                                            $demandeModel = new DemandeModel();
                                            echo htmlspecialchars($demandeModel->formatLeaveType($demande['type']));
                                        ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    <?= date('d/m/Y', strtotime($demande['date_debut'])) ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    <?= date('d/m/Y', strtotime($demande['date_fin'])) ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    <?php
                                                $start = new DateTime($demande['date_debut']);
                                                $end = new DateTime($demande['date_fin']);
                                                $interval = $start->diff($end);
                                                $days = $interval->days + 1;

                                                // Adjust for half-days if applicable
                                                if (isset($demande['demi_journee']) && $demande['demi_journee']) {
                                                    $demiJours = 0;
                                                    if (strpos($demande['demi_type'] ?? '', 'matin') !== false) {
                                                        $demiJours += 0.5;
                                                    }
                                                    if (strpos($demande['demi_type'] ?? '', 'apres-midi') !== false) {
                                                        $demiJours += 0.5;
                                                    }

                                                    if ($demiJours > 0) {
                                                        $days -= $demiJours;
                                                    }
                                                }

                                                echo $days . ' jour(s)';
                                            ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php
                                        $statut = strtolower($demande['statut']);
                                        if ($statut === 'en_attente_responsable'): ?>
                                <span
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    En attente responsable
                                </span>
                                <?php elseif ($statut === 'en_attente_planificateur'): ?>
                                <span
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-orange-100 text-orange-800">
                                    En attente planificateur
                                </span>
                                <?php elseif ($statut === 'approuvee'): ?>
                                <span
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Approuvée
                                </span>
                                <?php elseif ($statut === 'refusee'): ?>
                                <span
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    Rejetée
                                </span>
                                <?php elseif ($statut === 'annulee'): ?>
                                <span
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                    Annulée
                                </span>
                                <?php else: ?>
                                <span
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                    <?= htmlspecialchars(ucfirst($demande['statut'])) ?>
                                </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    <?php if (in_array(strtolower($demande['statut']), ['acceptée', 'approuvee', 'refusee'])): ?>
                                    <?php if (isset($demande['valideur_nom']) && isset($demande['valideur_prenom']) && !empty($demande['valideur_nom'])): ?>
                                    <?= htmlspecialchars($demande['valideur_prenom'] . ' ' . $demande['valideur_nom']) ?>
                                    <?php else: ?>
                                    Non renseigné
                                    <?php endif; ?>
                                    <?php else: ?>
                                    <span class="text-gray-400">-</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <div class="flex items-center space-x-2">
                                    <!-- View details action - always available -->
                                    <a href="/details-demande?id=<?= $demande['id'] ?>"
                                        class="text-indigo-600 hover:text-indigo-900 mr-1">
                                        <i class="fas fa-eye"></i> Voir détails
                                    </a>

                                    <!-- Cancel action - only for pending requests -->
                                    <?php if (in_array(strtolower($demande['statut']), ['en attente responsable', 'en attente planificateur'])): ?>
                                    <a href="/annuler-demande?id=<?= $demande['id'] ?>"
                                        class="text-red-600 hover:text-red-900"
                                        onclick="return confirm('Êtes-vous sûr de vouloir annuler cette demande ?')">
                                        <i class="fas fa-times"></i> Annuler
                                    </a>
                                    <?php endif; ?>

                                    <!-- Justificatif link - if available -->
                                    <?php if (!empty($demande['justificatif'])): ?>
                                    <a href="/uploads/justificatifs/<?= htmlspecialchars($demande['justificatif']) ?>"
                                        target="_blank" class="text-purple-600 hover:text-purple-800">
                                        <i class="fas fa-file-download"></i> Justificatif
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <?php if (isset($pagination) && $pagination['totalPages'] > 1): ?>
            <!-- Pagination -->
            <div class="px-6 py-4 bg-white border-t border-gray-200">
                <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                    <div class="text-sm text-gray-700 order-2 sm:order-1">
                        Affichage de <span
                            class="font-medium"><?= ($pagination['currentPage'] - 1) * $pagination['perPage'] + 1 ?></span>
                        à
                        <span
                            class="font-medium"><?= min($pagination['currentPage'] * $pagination['perPage'], $pagination['total']) ?></span>
                        sur
                        <span class="font-medium"><?= $pagination['total'] ?></span> résultats
                    </div>
                    <nav class="flex items-center space-x-1 order-1 sm:order-2" aria-label="Pagination" role="navigation">
                        <?php
                            // Previous page link
                            $prevPageUrl = '';
                            $prevDisabled = $pagination['currentPage'] <= 1;
                            if (!$prevDisabled) {
                                $urlParams = $_GET;
                                $urlParams['page'] = $pagination['currentPage'] - 1;
                                $prevPageUrl = '?' . http_build_query($urlParams);
                            }
                            ?>

                        <a href="<?= $prevDisabled ? '#' : $prevPageUrl ?>"
                            class="pagination-btn <?= $prevDisabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-200' ?> px-3 py-1 rounded-md bg-gray-100 text-gray-700 transition-colors duration-200"
                            <?= $prevDisabled ? 'onclick="return false;" aria-disabled="true"' : '' ?>
                            aria-label="Page précédente">
                            <i class="fas fa-chevron-left"></i>
                        </a>

                        <?php
                            // Page number links
                            $startPage = max(1, $pagination['currentPage'] - 2);
                            $endPage = min($pagination['totalPages'], $pagination['currentPage'] + 2);

                            // Always show first page if not in range
                            if ($startPage > 1) {
                                $urlParams = $_GET;
                                $urlParams['page'] = 1;
                                $pageUrl = '?' . http_build_query($urlParams);
                                echo '<a href="' . $pageUrl . '" class="pagination-btn px-3 py-1 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors duration-200" aria-label="Page 1">1</a>';

                                if ($startPage > 2) {
                                    echo '<span class="px-3 py-1 text-gray-500">...</span>';
                                }
                            }

                            // Page links in range
                            for ($i = $startPage; $i <= $endPage; $i++) {
                                $urlParams = $_GET;
                                $urlParams['page'] = $i;
                                $pageUrl = '?' . http_build_query($urlParams);

                                $isActive = $i === $pagination['currentPage'];
                                $activeClass = $isActive ? 'bg-purple-600 text-white font-medium' : 'bg-gray-100 text-gray-700 hover:bg-gray-200';
                                $ariaLabel = $isActive ? "Page $i (actuelle)" : "Page $i";
                                $ariaCurrent = $isActive ? 'aria-current="page"' : '';

                                echo '<a href="' . $pageUrl . '" class="pagination-btn px-3 py-1 rounded-md ' . $activeClass . ' transition-colors duration-200" ' . $ariaCurrent . ' aria-label="' . $ariaLabel . '">' . $i . '</a>';
                            }

                            // Always show last page if not in range
                            if ($endPage < $pagination['totalPages']) {
                                if ($endPage < $pagination['totalPages'] - 1) {
                                    echo '<span class="px-3 py-1 text-gray-500">...</span>';
                                }

                                $urlParams = $_GET;
                                $urlParams['page'] = $pagination['totalPages'];
                                $pageUrl = '?' . http_build_query($urlParams);
                                echo '<a href="' . $pageUrl . '" class="pagination-btn px-3 py-1 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors duration-200" aria-label="Page ' . $pagination['totalPages'] . '">' . $pagination['totalPages'] . '</a>';
                            }

                            // Next page link
                            $nextPageUrl = '';
                            $nextDisabled = $pagination['currentPage'] >= $pagination['totalPages'];
                            if (!$nextDisabled) {
                                $urlParams = $_GET;
                                $urlParams['page'] = $pagination['currentPage'] + 1;
                                $nextPageUrl = '?' . http_build_query($urlParams);
                            }
                            ?>

                        <a href="<?= $nextDisabled ? '#' : $nextPageUrl ?>"
                            class="pagination-btn <?= $nextDisabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-200' ?> px-3 py-1 rounded-md bg-gray-100 text-gray-700 transition-colors duration-200"
                            <?= $nextDisabled ? 'onclick="return false;" aria-disabled="true"' : '' ?>
                            aria-label="Page suivante">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </nav>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- Informations Utiles Section -->
        <div class="card mt-6">
            <h2 class="text-lg font-semibold mb-4"><i class="fas fa-info-circle text-indigo-500 mr-2"></i>Informations
                Utiles</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Politique Congés -->
                <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-book text-purple-600 mr-2"></i>
                        <h3 class="font-medium text-purple-800">Politique Congés</h3>
                    </div>
                    <p class="text-sm text-purple-700 mb-3">Consultez les règles et procédures pour les demandes de
                        congé</p>
                    <button onclick="openPolicyModal()"
                        class="bg-purple-600 text-white px-3 py-2 rounded-md hover:bg-purple-700 transition text-sm">
                        <i class="fas fa-eye mr-1"></i> Consulter
                    </button>
                </div>

                <!-- Guide de l'Employé -->
                <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-file-pdf text-blue-600 mr-2"></i>
                        <h3 class="font-medium text-blue-800">Guide de l'Employé</h3>
                    </div>
                    <p class="text-sm text-blue-700 mb-3">Consultez le guide complet des procédures</p>
                    <a href="/documents/serve?file=WORKFLOW.pdf" target="_blank"
                        class="bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 transition text-sm inline-block"
                        onclick="return openPDF(this)">
                        <i class="fas fa-eye mr-1"></i> Consulter
                    </a>
                </div>

                <!-- Documents RH -->
                <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-file-alt text-green-600 mr-2"></i>
                        <h3 class="font-medium text-green-800">Documents RH</h3>
                    </div>
                    <p class="text-sm text-green-700 mb-3">Demandez vos documents administratifs</p>
                    <div class="space-y-2">
                        <button onclick="requestDocument('attestation_travail')"
                            class="w-full bg-green-600 text-white px-3 py-1 rounded-md hover:bg-green-700 transition text-xs">
                            Attestation de travail
                        </button>
                        <button onclick="requestDocument('fiche_paie')"
                            class="w-full bg-green-600 text-white px-3 py-1 rounded-md hover:bg-green-700 transition text-xs">
                            Fiche de paie
                        </button>
                        <button onclick="requestDocument('attestation_salaire')"
                            class="w-full bg-green-600 text-white px-3 py-1 rounded-md hover:bg-green-700 transition text-xs">
                            Attestation de salaire
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Policy Modal -->
    <div id="policyModal"
        class="fixed inset-0 bg-gray-500 bg-opacity-75 hidden flex items-center justify-center z-50 p-4 transition-all duration-300 ease-in-out">
        <div
            class="modal-card bg-white rounded-lg shadow-xl max-w-lg w-full mx-4 transform transition-all duration-300 ease-in-out scale-95 opacity-0">
            <!-- Modal Header -->
            <div class="bg-purple-600 px-6 py-4 flex items-center justify-between">
                <div class="flex items-center">
                    <div
                        class="flex-shrink-0 w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-3">
                        <i class="fas fa-book text-white text-lg"></i>
                    </div>
                    <h3 class="text-lg font-medium text-white">Politique de Congés</h3>
                </div>
                <button type="button" class="text-white hover:text-gray-200 transition-colors duration-200"
                    onclick="closePolicyModal()">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="p-6">
                <div class="space-y-4">
                    <h4 class="font-semibold text-gray-800 mb-4">Rappel des règles :</h4>
                    <div class="space-y-4">
                        <div class="flex items-start p-4 bg-purple-50 rounded-lg border border-purple-200">
                            <div
                                class="flex-shrink-0 w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3 mt-0.5">
                                <i class="fas fa-calendar-check text-purple-600 text-sm"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-purple-800 mb-1">Congés payés</p>
                                <p class="text-sm text-purple-700">Les congés payés doivent être posés au moins 7 jours
                                    à l'avance.</p>
                            </div>
                        </div>

                        <div class="flex items-start p-4 bg-orange-50 rounded-lg border border-orange-200">
                            <div
                                class="flex-shrink-0 w-8 h-8 rounded-full bg-orange-100 flex items-center justify-center mr-3 mt-0.5">
                                <i class="fas fa-exclamation-triangle text-orange-600 text-sm"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-orange-800 mb-1">Congés exceptionnels</p>
                                <p class="text-sm text-orange-700">Les congés exceptionnels sont limités à 3 jours
                                    maximum.</p>
                            </div>
                        </div>

                        <div class="flex items-start p-4 bg-blue-50 rounded-lg border border-blue-200">
                            <div
                                class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3 mt-0.5">
                                <i class="fas fa-balance-scale text-blue-600 text-sm"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-blue-800 mb-1">Vérification du solde</p>
                                <p class="text-sm text-blue-700">Vérifiez votre solde avant de faire une demande.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end p-6 border-t border-gray-200 bg-gray-50">
                <button type="button"
                    class="modal-btn-primary px-6 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200"
                    onclick="closePolicyModal()">
                    <i class="fas fa-check mr-2"></i>Compris
                </button>
            </div>
        </div>
    </div>

    <!-- Document Success Modal -->
    <div id="document-success-modal"
        class="fixed inset-0 bg-gray-500 bg-opacity-75 hidden flex items-center justify-center z-50 p-4 transition-all duration-300 ease-in-out">
        <div
            class="modal-card bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-300 ease-in-out scale-95 opacity-0">
            <!-- Modal Header -->
            <div class="bg-green-600 px-6 py-4 flex items-center justify-between">
                <div class="flex items-center">
                    <div
                        class="flex-shrink-0 w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-3">
                        <i class="fas fa-check-circle text-white text-lg"></i>
                    </div>
                    <h3 class="text-lg font-medium text-white">Demande envoyée</h3>
                </div>
                <button type="button" class="text-white hover:text-gray-200 transition-colors duration-200"
                    onclick="closeDocumentModal('document-success-modal')">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="p-6 text-center">
                <div class="bg-green-100 border border-green-300 rounded-lg p-4 mb-4">
                    <i class="fas fa-check-circle text-green-500 text-4xl mb-3"></i>
                    <p class="text-gray-700" id="document-success-message"></p>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-center p-6 border-t border-gray-200 bg-gray-50">
                <button type="button"
                    class="modal-btn-primary px-6 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
                    onclick="closeDocumentModal('document-success-modal')">
                    <i class="fas fa-check mr-2"></i>OK
                </button>
            </div>
        </div>
    </div>

    <!-- Document Error Modal -->
    <div id="document-error-modal"
        class="fixed inset-0 bg-gray-500 bg-opacity-75 hidden flex items-center justify-center z-50 p-4 transition-all duration-300 ease-in-out">
        <div
            class="modal-card bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-300 ease-in-out scale-95 opacity-0">
            <!-- Modal Header -->
            <div class="bg-red-600 px-6 py-4 flex items-center justify-between">
                <div class="flex items-center">
                    <div
                        class="flex-shrink-0 w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-3">
                        <i class="fas fa-exclamation-triangle text-white text-lg"></i>
                    </div>
                    <h3 class="text-lg font-medium text-white">Erreur</h3>
                </div>
                <button type="button" class="text-white hover:text-gray-200 transition-colors duration-200"
                    onclick="closeDocumentModal('document-error-modal')">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="p-6 text-center">
                <div class="bg-red-100 border border-red-300 rounded-lg p-4 mb-4">
                    <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-3"></i>
                    <p class="text-gray-700" id="document-error-message"></p>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-center p-6 border-t border-gray-200 bg-gray-50">
                <button type="button"
                    class="modal-btn-primary px-6 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
                    onclick="closeDocumentModal('document-error-modal')">
                    <i class="fas fa-check mr-2"></i>OK
                </button>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filter elements
        const filterButton = document.getElementById('filterButton');
        const toggleFiltersButton = document.getElementById('toggleFilters');
        const filterPanel = document.getElementById('filterPanel');
        const periodFilter = document.getElementById('period');
        const statusFilter = document.getElementById('status');
        const typeFilter = document.getElementById('type');
        const searchInput = document.getElementById('search');
        const perPageSelect = document.getElementById('per_page');
        const sortHeaders = document.querySelectorAll('.sort-header');

        // Toggle filter panel
        toggleFiltersButton.addEventListener('click', function() {
            filterPanel.classList.toggle('hidden');
        });

        // Add event listener to filter button
        filterButton.addEventListener('click', applyFilters);

        // Add event listener to per page select
        perPageSelect.addEventListener('change', function() {
            applyFilters();
        });

        // Add event listeners to filter dropdowns for automatic filtering
        periodFilter.addEventListener('change', function() {
            applyFilters();
        });

        statusFilter.addEventListener('change', function() {
            applyFilters();
        });

        typeFilter.addEventListener('change', function() {
            applyFilters();
        });

        // Add event listeners to sort headers
        sortHeaders.forEach(header => {
            header.addEventListener('click', function(e) {
                e.preventDefault();
                const sortBy = this.getAttribute('data-sort');
                let sortOrder = 'ASC';

                // If already sorting by this column, toggle the order
                if (getUrlParameter('sort_by') === sortBy) {
                    sortOrder = getUrlParameter('sort_order') === 'ASC' ? 'DESC' : 'ASC';
                }

                // Apply sorting
                const urlParams = new URLSearchParams(window.location.search);
                urlParams.set('sort_by', sortBy);
                urlParams.set('sort_order', sortOrder);

                // Redirect to sorted URL
                window.location.href = window.location.pathname + '?' + urlParams.toString();
            });
        });

        // Add event listener for search input (with debounce)
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                applyFilters();
            }, 500);
        });

        function applyFilters(resetPage = true) {
            // Get filter values
            const period = periodFilter.value;
            const status = statusFilter.value;
            const type = typeFilter.value;
            const search = searchInput.value.trim();
            const perPage = perPageSelect.value;

            // Get current sort parameters
            const sortBy = getUrlParameter('sort_by') || 'date_demande';
            const sortOrder = getUrlParameter('sort_order') || 'DESC';

            // Get current page (reset to 1 when applying filters, preserve when navigating)
            const page = resetPage ? 1 : (getUrlParameter('page') || 1);

            // Create URL with query parameters
            let url = window.location.pathname + '?';
            let params = [];

            if (period !== 'all') params.push('period=' + period);
            if (status !== 'all') params.push('status=' + status);
            if (type !== 'all') params.push('type=' + type);
            if (search !== '') params.push('search=' + encodeURIComponent(search));
            if (perPage !== '5') params.push('per_page=' + perPage);
            if (page > 1) params.push('page=' + page);
            if (sortBy !== 'date_demande') params.push('sort_by=' + sortBy);
            if (sortOrder !== 'DESC') params.push('sort_order=' + sortOrder);

            url += params.join('&');

            // Update browser URL without refreshing
            const newUrl = params.length > 0 ? url : window.location.pathname;
            window.history.pushState({}, '', newUrl);

            // Show loading indicator
            showLoadingIndicator();

            // Make AJAX request
            fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    updateTableContent(data.demandes, data.pagination);
                } else {
                    console.error('Server returned error:', data);
                    showErrorMessage('Erreur lors du chargement des données');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage('Erreur de connexion. Veuillez réessayer.');
            })
            .finally(() => {
                hideLoadingIndicator();
            });
        }

        // New function to handle page navigation
        function navigateToPage(page) {
            // Update the URL parameter for page
            const urlParams = new URLSearchParams(window.location.search);
            if (page > 1) {
                urlParams.set('page', page);
            } else {
                urlParams.delete('page');
            }

            // Update browser URL
            const newUrl = urlParams.toString() ?
                window.location.pathname + '?' + urlParams.toString() :
                window.location.pathname;
            window.history.pushState({}, '', newUrl);

            // Apply filters without resetting page
            applyFilters(false);
        }

        // Helper function to get URL parameters
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // Preselect filters based on URL parameters
        function preselectFilters() {
            const urlParams = new URLSearchParams(window.location.search);

            if (urlParams.has('period')) {
                periodFilter.value = urlParams.get('period');
            }

            if (urlParams.has('status')) {
                statusFilter.value = urlParams.get('status');
            }

            if (urlParams.has('type')) {
                typeFilter.value = urlParams.get('type');
            }

            if (urlParams.has('search')) {
                searchInput.value = urlParams.get('search');
            }

            if (urlParams.has('per_page')) {
                perPageSelect.value = urlParams.get('per_page');
            }
        }

        // Initialize preselection
        preselectFilters();

        // Attach pagination event listeners for initial page load
        attachPaginationEventListeners();

        // Helper functions for AJAX functionality
        function showLoadingIndicator() {
            const tableContainer = document.querySelector('.table-card');
            if (tableContainer) {
                tableContainer.style.opacity = '0.6';
                tableContainer.style.pointerEvents = 'none';

                // Add loading class to pagination
                const paginationContainer = document.querySelector('nav[aria-label="Pagination"]');
                if (paginationContainer) {
                    paginationContainer.parentElement.classList.add('pagination-loading');
                }

                // Add loading spinner if not exists
                if (!document.querySelector('.loading-spinner')) {
                    const spinner = document.createElement('div');
                    spinner.className = 'loading-spinner';
                    spinner.innerHTML = `
                        <div class="flex items-center justify-center py-8">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                            <span class="ml-2 text-gray-600">Chargement...</span>
                        </div>
                    `;
                    tableContainer.appendChild(spinner);
                }
            }
        }

        function hideLoadingIndicator() {
            const tableContainer = document.querySelector('.table-card');
            const spinner = document.querySelector('.loading-spinner');

            if (tableContainer) {
                tableContainer.style.opacity = '1';
                tableContainer.style.pointerEvents = 'auto';
            }

            // Remove loading class from pagination
            const paginationContainer = document.querySelector('nav[aria-label="Pagination"]');
            if (paginationContainer) {
                paginationContainer.parentElement.classList.remove('pagination-loading');
            }

            if (spinner) {
                spinner.remove();
            }
        }

        function showErrorMessage(message) {
            // Remove existing error messages
            const existingError = document.querySelector('.ajax-error-message');
            if (existingError) {
                existingError.remove();
            }

            // Create error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'ajax-error-message bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
            errorDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-auto text-red-700 hover:text-red-900">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            // Insert before table
            const tableCard = document.querySelector('.table-card');
            if (tableCard) {
                tableCard.parentNode.insertBefore(errorDiv, tableCard);
            }
        }

        function updateTableContent(demandes, pagination) {
            const tableContainer = document.querySelector('.table-card');
            if (!tableContainer) return;

            // Generate new table HTML
            const newTableHTML = generateTableHTML(demandes, pagination);

            // Update the table container
            tableContainer.innerHTML = newTableHTML;

            // Re-attach event listeners for dynamically generated pagination
            attachPaginationEventListeners();
        }

        // Function to attach event listeners to pagination controls
        function attachPaginationEventListeners() {
            // Add event listeners to dynamically generated pagination buttons
            const paginationContainer = document.querySelector('nav[aria-label="Pagination"]');
            if (paginationContainer) {
                // Use event delegation for pagination buttons
                paginationContainer.addEventListener('click', function(e) {
                    const button = e.target.closest('.pagination-btn');
                    if (button && !button.disabled && button.onclick) {
                        e.preventDefault();
                        // The onclick handler will be executed automatically
                    }
                });

                // Add keyboard navigation support
                paginationContainer.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        const button = e.target.closest('.pagination-btn');
                        if (button && !button.disabled) {
                            e.preventDefault();
                            button.click();
                        }
                    }
                });
            }
        }

        function generateTableHTML(demandes, pagination) {
            if (!demandes || demandes.length === 0) {
                return `
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead>
                                <tr class="bg-gray-50">
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Référence</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Début</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Fin</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Validé par</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                                        <div class="flex flex-col items-center">
                                            <i class="fas fa-calendar-times text-4xl text-gray-300 mb-4"></i>
                                            <p class="text-lg mb-4">Aucune demande trouvée avec les filtres sélectionnés.</p>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                `;
            }

            // Generate table rows
            let tableRows = '';
            demandes.forEach(demande => {
                tableRows += generateTableRow(demande);
            });

            // Generate pagination HTML
            const paginationHTML = generatePaginationHTML(pagination);

            return `
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Référence</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Début</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Fin</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Validé par</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${tableRows}
                        </tbody>
                    </table>
                </div>
                ${paginationHTML}
            `;
        }

        function generateTableRow(demande) {
            // Format leave type
            const typeMap = {
                'payé': 'Congé payé',
                'sans solde': 'Congé sans solde',
                'maladie': 'Congé maladie',
                'exceptionnel': 'Congé exceptionnel'
            };
            const formattedType = typeMap[demande.type] || demande.type;

            // Format dates
            const dateDebut = new Date(demande.date_debut).toLocaleDateString('fr-FR');
            const dateFin = new Date(demande.date_fin).toLocaleDateString('fr-FR');

            // Calculate duration
            const start = new Date(demande.date_debut);
            const end = new Date(demande.date_fin);
            let days = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;

            // Adjust for half-days if applicable
            if (demande.demi_journee) {
                let demiJours = 0;
                if (demande.demi_type && demande.demi_type.includes('matin')) demiJours += 0.5;
                if (demande.demi_type && demande.demi_type.includes('apres-midi')) demiJours += 0.5;
                if (demiJours > 0) days -= demiJours;
            }

            // Format status
            const statusMap = {
                'en_attente_responsable': { class: 'bg-yellow-100 text-yellow-800', text: 'En attente responsable' },
                'en_attente_planificateur': { class: 'bg-orange-100 text-orange-800', text: 'En attente planificateur' },
                'approuvee': { class: 'bg-green-100 text-green-800', text: 'Approuvée' },
                'refusee': { class: 'bg-red-100 text-red-800', text: 'Rejetée' },
                'annulee': { class: 'bg-gray-100 text-gray-800', text: 'Annulée' }
            };
            const statusInfo = statusMap[demande.statut.toLowerCase()] || { class: 'bg-blue-100 text-blue-800', text: demande.statut };

            // Format validator
            let validatorText = '<span class="text-gray-400">-</span>';
            if (['acceptée', 'approuvee', 'refusee'].includes(demande.statut.toLowerCase())) {
                if (demande.valideur_nom && demande.valideur_prenom) {
                    validatorText = `${demande.valideur_prenom} ${demande.valideur_nom}`;
                } else {
                    validatorText = 'Non renseigné';
                }
            }

            // Generate actions
            let actions = `<a href="/details-demande?id=${demande.id}" class="text-indigo-600 hover:text-indigo-900 mr-1">
                <i class="fas fa-eye"></i> Voir détails
            </a>`;

            // Add cancel action for pending requests
            if (['en attente responsable', 'en attente planificateur'].includes(demande.statut.toLowerCase())) {
                actions += ` <a href="/annuler-demande?id=${demande.id}" class="text-red-600 hover:text-red-900"
                    onclick="return confirm('Êtes-vous sûr de vouloir annuler cette demande ?')">
                    <i class="fas fa-times"></i> Annuler
                </a>`;
            }

            // Add justificatif link if available
            if (demande.justificatif) {
                actions += ` <a href="/uploads/justificatifs/${demande.justificatif}" target="_blank" class="text-purple-600 hover:text-purple-800">
                    <i class="fas fa-file-download"></i> Justificatif
                </a>`;
            }

            return `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">
                            ${demande.reference_demande || '<span class="text-gray-400">-</span>'}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${formattedType}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${dateDebut}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${dateFin}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${days} jour(s)</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusInfo.class}">
                            ${statusInfo.text}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${validatorText}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <div class="flex items-center space-x-2">${actions}</div>
                    </td>
                </tr>
            `;
        }

        function generatePaginationHTML(pagination) {
            if (!pagination || pagination.totalPages <= 1) {
                return '';
            }

            const currentPage = pagination.currentPage;
            const totalPages = pagination.totalPages;

            // Generate Previous button
            const prevDisabled = currentPage <= 1;
            const prevButton = `
                <button onclick="navigateToPage(${currentPage - 1})"
                        class="pagination-btn ${prevDisabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-200'} px-3 py-1 rounded-md bg-gray-100 text-gray-700 transition-colors duration-200"
                        ${prevDisabled ? 'disabled' : ''}
                        aria-label="Page précédente">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;

            // Generate Next button
            const nextDisabled = currentPage >= totalPages;
            const nextButton = `
                <button onclick="navigateToPage(${currentPage + 1})"
                        class="pagination-btn ${nextDisabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-200'} px-3 py-1 rounded-md bg-gray-100 text-gray-700 transition-colors duration-200"
                        ${nextDisabled ? 'disabled' : ''}
                        aria-label="Page suivante">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;

            // Generate page number buttons
            let pageButtons = '';

            // Calculate page range to show
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            // Always show first page if not in range
            if (startPage > 1) {
                pageButtons += `
                    <button onclick="navigateToPage(1)"
                            class="pagination-btn px-3 py-1 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors duration-200"
                            aria-label="Page 1">
                        1
                    </button>
                `;

                if (startPage > 2) {
                    pageButtons += '<span class="px-3 py-1 text-gray-500">...</span>';
                }
            }

            // Generate page number buttons in range
            for (let i = startPage; i <= endPage; i++) {
                const isActive = i === currentPage;
                const activeClass = isActive ? 'bg-purple-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200';
                const ariaLabel = isActive ? `Page ${i} (actuelle)` : `Page ${i}`;

                pageButtons += `
                    <button onclick="navigateToPage(${i})"
                            class="pagination-btn px-3 py-1 rounded-md ${activeClass} transition-colors duration-200 ${isActive ? 'font-medium' : ''}"
                            ${isActive ? 'aria-current="page"' : ''}
                            aria-label="${ariaLabel}">
                        ${i}
                    </button>
                `;
            }

            // Always show last page if not in range
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    pageButtons += '<span class="px-3 py-1 text-gray-500">...</span>';
                }

                pageButtons += `
                    <button onclick="navigateToPage(${totalPages})"
                            class="pagination-btn px-3 py-1 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors duration-200"
                            aria-label="Page ${totalPages}">
                        ${totalPages}
                    </button>
                `;
            }

            return `
                <div class="px-6 py-4 bg-white border-t border-gray-200">
                    <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                        <div class="text-sm text-gray-700 order-2 sm:order-1">
                            Affichage de <span class="font-medium">${(pagination.currentPage - 1) * pagination.perPage + 1}</span>
                            à <span class="font-medium">${Math.min(pagination.currentPage * pagination.perPage, pagination.total)}</span>
                            sur <span class="font-medium">${pagination.total}</span> résultats
                        </div>
                        <nav class="flex items-center space-x-1 order-1 sm:order-2" aria-label="Pagination" role="navigation">
                            ${prevButton}
                            ${pageButtons}
                            ${nextButton}
                        </nav>
                    </div>
                </div>
            `;
        }
    });

    // Policy Modal Functions with animations
    function openPolicyModal() {
        const modal = document.getElementById('policyModal');
        const modalCard = modal.querySelector('.modal-card');

        // Show modal
        modal.classList.remove('hidden');

        // Trigger animation after a small delay to ensure the element is rendered
        setTimeout(() => {
            modal.classList.add('modal-show');
            modalCard.classList.remove('scale-95', 'opacity-0');
            modalCard.classList.add('scale-100', 'opacity-100');
        }, 10);

        // Prevent body scroll
        document.body.style.overflow = 'hidden';
    }

    function closePolicyModal() {
        const modal = document.getElementById('policyModal');
        const modalCard = modal.querySelector('.modal-card');

        // Start closing animation
        modal.classList.remove('modal-show');
        modalCard.classList.remove('scale-100', 'opacity-100');
        modalCard.classList.add('scale-95', 'opacity-0');

        // Hide modal after animation completes
        setTimeout(() => {
            modal.classList.add('hidden');
            // Restore body scroll
            document.body.style.overflow = '';
        }, 300);
    }

    // Document Request Function
    function requestDocument(type) {
        // Map document types to user-friendly names
        const documentNames = {
            'attestation_travail': 'Attestation de travail',
            'fiche_paie': 'Fiche de paie',
            'attestation_salaire': 'Attestation de salaire'
        };

        const documentName = documentNames[type] || type;

        // Send AJAX request to create document request immediately
        fetch('/demande-document', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    type: type,
                    document_name: documentName
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showDocumentSuccessModal('Votre demande sera envoyée au service RH pour traitement');
                } else {
                    showDocumentErrorModal(`Erreur lors de l'envoi de votre demande : ${data.message || 'Erreur inconnue'}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showDocumentErrorModal('Erreur lors de l\'envoi de votre demande. Veuillez réessayer plus tard.');
            });
    }

    // Show success modal for document requests
    function showDocumentSuccessModal(message) {
        const modal = document.getElementById('document-success-modal');
        const messageElement = document.getElementById('document-success-message');
        messageElement.textContent = message;

        modal.classList.remove('hidden');
        const modalCard = modal.querySelector('.modal-card');

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Start opening animation
        setTimeout(() => {
            modal.classList.add('modal-show');
            modalCard.classList.remove('scale-95', 'opacity-0');
            modalCard.classList.add('scale-100', 'opacity-100');
        }, 10);
    }

    // Show error modal for document requests
    function showDocumentErrorModal(message) {
        const modal = document.getElementById('document-error-modal');
        const messageElement = document.getElementById('document-error-message');
        messageElement.textContent = message;

        modal.classList.remove('hidden');
        const modalCard = modal.querySelector('.modal-card');

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Start opening animation
        setTimeout(() => {
            modal.classList.add('modal-show');
            modalCard.classList.remove('scale-95', 'opacity-0');
            modalCard.classList.add('scale-100', 'opacity-100');
        }, 10);
    }

    // Close document modal
    function closeDocumentModal(modalId) {
        const modal = document.getElementById(modalId);
        const modalCard = modal.querySelector('.modal-card');

        // Start closing animation
        modal.classList.remove('modal-show');
        modalCard.classList.remove('scale-100', 'opacity-100');
        modalCard.classList.add('scale-95', 'opacity-0');

        // Hide modal after animation completes
        setTimeout(() => {
            modal.classList.add('hidden');
            // Restore body scroll
            document.body.style.overflow = '';
        }, 300);
    }

    // PDF opening function with error handling
    function openPDF(linkElement) {
        try {
            // Open the PDF in a new tab
            const newWindow = window.open(linkElement.href, '_blank');

            // Check if the window was blocked by popup blocker
            if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
                alert('Le popup a été bloqué. Veuillez autoriser les popups pour ce site et réessayer.');
                return false;
            }

            // Optional: Check if the PDF loaded successfully after a short delay
            setTimeout(() => {
                try {
                    if (newWindow.closed) {
                        // Window was closed, might indicate an error
                        console.log('PDF window was closed');
                    }
                } catch (e) {
                    // Cross-origin error is expected and normal
                    console.log('PDF opened successfully');
                }
            }, 1000);

            return false; // Prevent default link behavior
        } catch (error) {
            console.error('Error opening PDF:', error);
            alert('Erreur lors de l\'ouverture du PDF. Veuillez réessayer.');
            return false;
        }
    }

    // Enhanced modal event listeners
    document.addEventListener('DOMContentLoaded', function() {
        const policyModal = document.getElementById('policyModal');

        // Close modal when clicking outside
        policyModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closePolicyModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !policyModal.classList.contains('hidden')) {
                closePolicyModal();
            }
        });

        // Prevent modal content clicks from closing the modal
        policyModal.querySelector('.modal-card').addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });
    </script>
</body>

</html>